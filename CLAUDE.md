# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个拼多多商品数据爬虫系统，包含Python后端爬虫。系统使用Playwright进行网页自动化，提供REST API和WebSocket接口。

## 常用命令

### 后端运行
```bash
# 安装依赖
pip install -r requirements.txt

# 运行主爬虫程序（命令行模式）
python run_main.py --keyword "手机,冰箱" --max_products 100

# 启动API服务器（提供REST API和WebSocket服务）
python backend/api_server.py --port 8000

# 使用自动端口选择启动
python start_with_auto_port.py
```

### 测试和调试
```bash
# 运行Python测试
python -m pytest tests/

# 检查代码质量
python -m flake8 src/
```

## 核心架构

### 后端架构
- **src/main.py**: 主爬虫类PDDCrawler，实现核心爬取逻辑
- **backend/api_server.py**: FastAPI服务器，提供REST API和WebSocket接口
- **src/core/**: 核心模块目录
  - `browser_manager.py`: 浏览器管理，支持CDP模式
  - `api_response_monitor.py`: API响应监控，拦截商品数据
  - `anti_detection_simple.py`: 反检测措施
  - `cookie_manager.py`: Cookie管理
  - `scroll_manager.py`: 智能滚动管理
- **src/data/**: 数据处理模块
  - `processor.py`: 数据处理和筛选
  - `exporter.py`: Excel/CSV导出
- **src/monitoring/**: 监控模块
  - `memory_monitor.py`: 内存监控和告警
- **src/utils/**: 工具模块
  - `port_manager.py`: 端口管理器，自动选择可用端口
  - `resource_cleaner.py`: 资源清理器

### WebSocket通信协议
系统通过WebSocket提供实时通信，消息类型包括：
- `connected`: 连接建立
- `progress`: 进度更新
- `data`: 商品数据推送
- `log`: 日志消息
- `status`: 状态变更
- `completed`: 任务完成
- `error`: 错误信息

## 关键特性

1. **多关键词顺序爬取**: 支持一次输入多个关键词，系统会按顺序爬取
2. **实时数据推送**: 通过WebSocket实时推送爬取进度和数据
3. **智能反检测**: 包含多种反检测措施，模拟真实用户行为
4. **内存监控**: 实时监控各组件内存使用，防止内存泄漏
5. **自动端口管理**: 自动检测和分配可用端口
6. **Cookie管理**: 支持Cookie导入/导出，保持登录状态
7. **数据筛选**: 支持基于品牌、规格等条件的商品筛选

## 配置文件

- **config/settings.yaml**: 主配置文件，包含所有系统参数
- **config/ports.json**: 端口配置
- **config/cookies.json**: Cookie存储

## 注意事项

1. **CDP模式**: 系统优先使用Chrome DevTools Protocol连接现有浏览器，需要先启动Chrome
2. **登录状态**: 首次使用需要手动登录拼多多，系统会保存登录状态
3. **内存管理**: 长时间运行可能占用较多内存，系统有自动清理机制
4. **端口冲突**: 如遇端口冲突，系统会自动选择其他可用端口
5. **数据格式**: 支持导出Excel和CSV格式，默认保存在output目录

## 调试技巧

1. 检查日志文件：`logs/api_server.log` 和 `logs/pdd_crawler_*.log`
2. 使用浏览器开发者工具查看WebSocket消息
3. 配置文件中启用debug模式获取更详细日志
4. 使用`memory_report_*.json`文件分析内存使用情况

## 常见问题

1. **浏览器连接失败**: 确保Chrome已启动且启用了远程调试端口
2. **Cookie过期**: 重新登录或手动更新Cookie
3. **内存占用过高**: 检查内存监控报告，调整配置中的阈值
4. **数据不完整**: 检查网络状态和反检测设置