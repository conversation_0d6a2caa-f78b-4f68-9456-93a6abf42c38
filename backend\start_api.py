#!/usr/bin/env python3
"""
API服务器启动脚本
自动检测依赖并启动优化版API服务器
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查并安装依赖"""
    requirements_file = Path(__file__).parent / "requirements_api.txt"
    
    if not requirements_file.exists():
        print("❌ requirements_api.txt 文件不存在")
        return False
    
    try:
        print("🔍 检查FastAPI依赖...")
        import fastapi
        import uvicorn
        print("✅ FastAPI依赖已安装")
        return True
    except ImportError:
        print("📦 安装FastAPI依赖...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "-r", str(requirements_file)
            ])
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False

def find_available_port(start_port=8000):
    """查找可用端口"""
    import socket
    
    for port in range(start_port, start_port + 10):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                if result != 0:  # 端口可用
                    return port
        except:
            continue
    return start_port  # 回退到默认端口

def main():
    """主函数"""
    print("🚀 启动拼多多爬虫API服务器...")
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，退出")
        sys.exit(1)
    
    # 查找可用端口
    port = find_available_port()
    print(f"📍 使用端口: {port}")
    
    # 启动服务器
    try:
        import uvicorn
        from optimized_api_server import app
        
        print(f"🌐 服务地址: http://localhost:{port}")
        print(f"📖 API文档: http://localhost:{port}/docs")
        print("🔧 这是优化版API服务器，稳定且高效")
        print("按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            "optimized_api_server:app",
            host="0.0.0.0",
            port=port,
            reload=False,  # 生产环境关闭自动重载
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
