"""
端口管理工具 - 统一管理前后端端口配置
"""
import json
import socket
import os
import subprocess
import sys
import time
from pathlib import Path

class PortManager:
    def __init__(self):
        self.config_path = Path(__file__).parent.parent / "config" / "ports.json"
        self.config = self.load_config()
        
    def load_config(self):
        """加载端口配置"""
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise FileNotFoundError(f"端口配置文件不存在: {self.config_path}")
    
    def save_config(self):
        """保存端口配置"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def is_port_available(self, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                return result != 0
        except:
            return False
    
    def find_process_by_port(self, port):
        """查找占用指定端口的进程"""
        try:
            if sys.platform == "win32":
                # Windows
                cmd = f"netstat -ano | findstr :{port}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if "LISTENING" in line:
                            parts = line.split()
                            pid = parts[-1]
                            return int(pid)
            else:
                # Linux/Mac
                cmd = f"lsof -i :{port} | grep LISTEN"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    parts = result.stdout.strip().split()
                    if len(parts) > 1:
                        return int(parts[1])
        except:
            pass
        return None
    
    def kill_process_by_port(self, port):
        """杀死占用指定端口的进程"""
        pid = self.find_process_by_port(port)
        if pid:
            try:
                if sys.platform == "win32":
                    subprocess.run(f"taskkill /PID {pid} /F", shell=True, capture_output=True)
                else:
                    subprocess.run(f"kill -9 {pid}", shell=True, capture_output=True)
                print(f"已终止占用端口 {port} 的进程 (PID: {pid})")
                time.sleep(1)  # 等待端口释放
                return True
            except:
                print(f"无法终止进程 {pid}")
                return False
        return True
    
    def get_available_port(self, service_type):
        """获取可用端口"""
        service_config = self.config.get(service_type)
        if not service_config:
            raise ValueError(f"未知的服务类型: {service_type}")
        
        # 首先尝试默认端口
        default_port = service_config['default']
        if self.is_port_available(default_port):
            return default_port
        
        # 如果默认端口被占用，尝试清理
        print(f"端口 {default_port} 被占用，尝试清理...")
        if self.kill_process_by_port(default_port) and self.is_port_available(default_port):
            return default_port
        
        # 尝试备用端口
        for port in service_config['fallback']:
            if self.is_port_available(port):
                print(f"使用备用端口: {port}")
                return port
        
        # 所有端口都被占用，找一个随机可用端口
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            port = s.getsockname()[1]
            print(f"使用随机端口: {port}")
            return port
    
    def update_current_ports(self, api_port=None):
        """更新当前使用的端口"""
        if api_port:
            self.config['currentPorts']['api'] = api_port
        self.save_config()
    
    def get_current_ports(self):
        """获取当前使用的端口"""
        return self.config['currentPorts']
    
    def update_env_files(self, api_port):
        """更新环境变量文件（前端已移除，保留方法以兼容现有代码）"""
        print(f"API端口配置: {api_port}")


def main():
    """主函数 - 用于测试"""
    manager = PortManager()

    # 获取可用端口
    api_port = manager.get_available_port('api')

    print(f"API端口: {api_port}")

    # 更新配置
    manager.update_current_ports(api_port)
    manager.update_env_files(api_port)


if __name__ == "__main__":
    main()