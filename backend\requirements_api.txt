# === FastAPI后端依赖配置 ===
# 更新时间: 2025-01-06
# 用于拼多多爬虫API服务器

# === 核心Web框架 ===
fastapi>=0.110.0              # 现代化Web框架
uvicorn[standard]>=0.29.0     # ASGI服务器，支持WebSocket
python-multipart>=0.0.6      # 支持表单数据处理

# === 数据验证和序列化 ===
pydantic>=2.0.0               # 数据验证和序列化

# === WebSocket支持 ===
websockets>=12.0              # WebSocket协议支持

# === 配置和工具 ===
python-dotenv>=1.0.0          # 环境变量管理
aiofiles>=23.0.0              # 异步文件操作

# === 日志和监控 ===
loguru>=0.7.0                 # 现代化日志库

# === 开发工具 (可选) ===
# pytest>=7.0.0              # 测试框架
# pytest-asyncio>=0.21.0     # 异步测试支持
# httpx>=0.25.0               # HTTP客户端，用于测试
