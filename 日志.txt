Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
websocket.ts:38 正在连接WebSocket: ws://localhost:8000/ws/crawl/default
websocket.ts:55 WebSocket connection to 'ws://localhost:8000/ws/crawl/default' failed: WebSocket is closed before the connection is established.
disconnect @ websocket.ts:55
chunk-GAIRK6MO.js?v=dff41fc2:1270 Warning: [antd: compatible] antd v5 support React is 16 ~ 18. see https://u.ant.design/v5-for-19 for compatible.
warning @ chunk-GAIRK6MO.js?v=dff41fc2:1270
websocket.ts:38 正在连接WebSocket: ws://localhost:8000/ws/crawl/default
api.ts:16 API Request: GET /status undefined
api.ts:16 API Request: GET /products undefined
api.ts:16 API Request: GET /status undefined
api.ts:16 API Request: GET /products undefined
websocket.ts:136 WebSocket错误: Event
ws.onerror @ websocket.ts:136
App.tsx:51 WebSocket错误: Event
（匿名） @ App.tsx:51
websocket.ts:127 WebSocket连接关闭: 1006 
:5174/api/status:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.ts:32 API Response Error: 404 Object
（匿名） @ api.ts:32
api.ts:159 后端连接检查失败: Error: Request failed with status code 404
    at api.ts:34:27
    at async Axios.request (axios.js?v=63bee55f:2139:14)
    at async checkConnection (api.ts:156:5)
    at async checkBackendConnection (App.tsx:60:25)
checkConnection @ api.ts:159
:5174/api/products:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.ts:32 API Response Error: 404 Object
（匿名） @ api.ts:32
api.ts:100 获取商品数据失败，返回空数组: Error: Request failed with status code 404
    at api.ts:34:27
    at async Axios.request (axios.js?v=63bee55f:2139:14)
    at async getProducts (api.ts:97:22)
    at async loadInitialData (App.tsx:82:33)
getProducts @ api.ts:100
:5174/api/status:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.ts:32 API Response Error: 404 Object
（匿名） @ api.ts:32
api.ts:159 后端连接检查失败: Error: Request failed with status code 404
    at api.ts:34:27
    at async Axios.request (axios.js?v=63bee55f:2139:14)
    at async checkConnection (api.ts:156:5)
    at async checkBackendConnection (App.tsx:60:25)
checkConnection @ api.ts:159
 Script was injected!
:5174/api/products:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.ts:32 API Response Error: 404 Object
（匿名） @ api.ts:32
api.ts:100 获取商品数据失败，返回空数组: Error: Request failed with status code 404
    at api.ts:34:27
    at async Axios.request (axios.js?v=63bee55f:2139:14)
    at async getProducts (api.ts:97:22)
    at async loadInitialData (App.tsx:82:33)
getProducts @ api.ts:100
content.js:202 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
websocket.ts:98 WebSocket连接成功
websocket.ts:107 WebSocket收到消息: Object
websocket.ts:127 WebSocket连接关闭: 1008 Task not found
websocket.ts:155 WebSocket重连尝试 1/5
websocket.ts:38 正在连接WebSocket: ws://localhost:8000/ws/crawl/default
websocket.ts:98 WebSocket连接成功
websocket.ts:107 WebSocket收到消息: Object
websocket.ts:127 WebSocket连接关闭: 1008 Task not found
websocket.ts:155 WebSocket重连尝试 1/5
websocket.ts:38 正在连接WebSocket: ws://localhost:8000/ws/crawl/default
websocket.ts:98 WebSocket连接成功
content_script.js:7112 Uncaught (in promise) fetchError: Failed to fetch
    at ZC (content_script.js:7112:26987)
    at E4.sendMessage (content_script.js:7112:26416)
    at async Ze (content_script.js:8247:8034)
    at async content_script.js:8247:25384
ZC @ content_script.js:7112
sendMessage @ content_script.js:7112
websocket.ts:107 WebSocket收到消息: {type: 'error', data: {…}}
websocket.ts:127 WebSocket连接关闭: 1008 Task not found
websocket.ts:155 WebSocket重连尝试 1/5
3content_script.js:7112 Uncaught (in promise) fetchError: Failed to fetch
    at ZC (content_script.js:7112:26987)
    at E4.sendMessage (content_script.js:7112:26416)
    at async Ze (content_script.js:8247:8034)
    at async content_script.js:8247:25384


    2025-08-05 18:21:24.956 | INFO     | __main__:<module>:83 - ============================================================
2025-08-05 18:21:24.957 | INFO     | __main__:<module>:84 - API服务器启动初始化
2025-08-05 18:21:24.957 | INFO     | __main__:<module>:85 - Python版本: 3.11.0rc2 (main, Sep 11 2022, 20:22:52) [MSC v.1933 64 bit (AMD64)]
2025-08-05 18:21:24.957 | INFO     | __main__:<module>:86 - 工作目录: c:\Users\<USER>\Desktop\pdd2
2025-08-05 18:21:24.957 | INFO     | __main__:<module>:87 - 项目根目录: c:\Users\<USER>\Desktop\pdd2
2025-08-05 18:21:24.957 | INFO     | __main__:<module>:88 - Python路径: ['c:\\Users\\<USER>\\Desktop\\pdd2', 'c:\\Users\\<USER>\\Desktop\\pdd2\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\superclaude-3.0.0-py3.11.egg', 'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin']
2025-08-05 18:21:24.958 | INFO     | __main__:<module>:89 - ============================================================
2025-08-05 18:21:25.353 | INFO     | __main__:<module>:96 - FastAPI相关模块导入成功
2025-08-05 18:21:25.658 | INFO     | src.data.processor:<module>:17 - RapidFuzz库已成功导入，启用高性能品牌识别
2025-08-05 18:21:26.392 | DEBUG    | src.utils.helpers:load_config:32 - 成功加载配置文件: config/settings.yaml
2025-08-05 18:21:26.393 | INFO     | src.utils.retry:__init__:36 - 重试管理器初始化完成（优化版）
2025-08-05 18:21:26.401 | INFO     | __main__:<module>:104 - PDDCrawler导入成功
2025-08-05 18:21:26.402 | INFO     | __main__:<module>:114 - load_config导入成功
2025-08-05 18:21:26.403 | INFO     | __main__:<module>:121 - CookieManager导入成功
2025-08-05 18:21:26.403 | INFO     | src.core.cookie_manager:__init__:30 - Cookie管理器初始化完成，存储目录: c:\Users\<USER>\Desktop\pdd2\browser_data\cookies
已更新前端环境变量文件: API端口=8000
2025-08-05 18:21:27.450 | INFO     | __main__:<module>:1590 - 启动API服务器...
2025-08-05 18:21:27.451 | INFO     | __main__:<module>:1591 - Python版本: 3.11.0rc2 (main, Sep 11 2022, 20:22:52) [MSC v.1933 64 bit (AMD64)]
2025-08-05 18:21:27.451 | INFO     | __main__:<module>:1592 - 工作目录: c:\Users\<USER>\Desktop\pdd2
2025-08-05 18:21:27.454 | INFO     | __main__:<module>:1593 - 项目根目录: c:\Users\<USER>\Desktop\pdd2
2025-08-05 18:21:27.454 | INFO     | __main__:<module>:1613 - 目录检查完成，启动uvicorn服务器...
2025-08-05 18:21:27.455 | INFO     | __main__:<module>:1616 - API服务器将在端口 8000 上启动
2025-08-05 18:21:27.511 | INFO     | backend.api_server:<module>:83 - ============================================================
2025-08-05 18:21:27.512 | INFO     | backend.api_server:<module>:84 - API服务器启动初始化
2025-08-05 18:21:27.512 | INFO     | backend.api_server:<module>:85 - Python版本: 3.11.0rc2 (main, Sep 11 2022, 20:22:52) [MSC v.1933 64 bit (AMD64)]
2025-08-05 18:21:27.512 | INFO     | backend.api_server:<module>:86 - 工作目录: c:\Users\<USER>\Desktop\pdd2
2025-08-05 18:21:27.513 | INFO     | backend.api_server:<module>:87 - 项目根目录: c:\Users\<USER>\Desktop\pdd2
2025-08-05 18:21:27.513 | INFO     | backend.api_server:<module>:88 - Python路径: ['c:\\Users\\<USER>\\Desktop\\pdd2', 'c:\\Users\\<USER>\\Desktop\\pdd2', 'c:\\Users\\<USER>\\Desktop\\pdd2', 'c:\\Users\\<USER>\\Desktop\\pdd2', 'c:\\Users\\<USER>\\Desktop\\pdd2\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\superclaude-3.0.0-py3.11.egg', 'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python311\\lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin']
2025-08-05 18:21:27.513 | INFO     | backend.api_server:<module>:89 - ============================================================
2025-08-05 18:21:27.514 | INFO     | backend.api_server:<module>:96 - FastAPI相关模块导入成功
2025-08-05 18:21:27.514 | INFO     | backend.api_server:<module>:104 - PDDCrawler导入成功
2025-08-05 18:21:27.514 | INFO     | backend.api_server:<module>:114 - load_config导入成功
2025-08-05 18:21:27.514 | INFO     | backend.api_server:<module>:121 - CookieManager导入成功
2025-08-05 18:21:27.515 | INFO     | src.core.cookie_manager:__init__:30 - Cookie管理器初始化完成，存储目录: c:\Users\<USER>\Desktop\pdd2\browser_data\cookies
INFO:     Started server process [58948]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     127.0.0.1:54809 - "GET /api/status HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54810 - "GET /api/products HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54812 - "GET /api/status HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54814 - "GET /api/products HTTP/1.1" 404 Not Found
INFO:     ('127.0.0.1', 54820) - "WebSocket /ws/crawl/default" [accepted]
2025-08-05 18:21:40.644 | INFO     | backend.api_server:websocket_endpoint:782 - WebSocket连接已建立: task_id=default
2025-08-05 18:21:40.644 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (1/10)
INFO:     connection open
2025-08-05 18:21:41.138 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (2/10)
2025-08-05 18:21:41.665 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (3/10)
2025-08-05 18:21:42.161 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (4/10)
2025-08-05 18:21:42.673 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (5/10)
2025-08-05 18:21:43.169 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (6/10)
2025-08-05 18:21:43.681 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (7/10)
2025-08-05 18:21:44.208 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (8/10)
2025-08-05 18:21:44.721 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (9/10)
2025-08-05 18:21:45.217 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (10/10)
2025-08-05 18:21:45.729 | WARNING  | backend.api_server:websocket_endpoint:794 - WebSocket连接请求的任务ID不存在: default
INFO:     connection closed
INFO:     ('127.0.0.1', 54851) - "WebSocket /ws/crawl/default" [accepted]
2025-08-05 18:21:49.066 | INFO     | backend.api_server:websocket_endpoint:782 - WebSocket连接已建立: task_id=default
2025-08-05 18:21:49.067 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (1/10)
INFO:     connection open
2025-08-05 18:21:49.577 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (2/10)
2025-08-05 18:21:50.105 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (3/10)
2025-08-05 18:21:50.617 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (4/10)
2025-08-05 18:21:51.129 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (5/10)
2025-08-05 18:21:51.641 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (6/10)
2025-08-05 18:21:52.138 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (7/10)
2025-08-05 18:21:52.664 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (8/10)
2025-08-05 18:21:53.176 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (9/10)
2025-08-05 18:21:53.688 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (10/10)
2025-08-05 18:21:54.215 | WARNING  | backend.api_server:websocket_endpoint:794 - WebSocket连接请求的任务ID不存在: default
INFO:     connection closed
INFO:     ('127.0.0.1', 54892) - "WebSocket /ws/crawl/default" [accepted]
2025-08-05 18:21:57.580 | INFO     | backend.api_server:websocket_endpoint:782 - WebSocket连接已建立: task_id=default
2025-08-05 18:21:57.581 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (1/10)
INFO:     connection open
2025-08-05 18:21:58.105 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (2/10)
2025-08-05 18:21:58.617 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (3/10)
2025-08-05 18:21:59.129 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (4/10)
2025-08-05 18:21:59.641 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (5/10)
2025-08-05 18:22:00.136 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (6/10)
2025-08-05 18:22:00.648 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (7/10)
2025-08-05 18:22:01.145 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (8/10)
2025-08-05 18:22:01.655 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (9/10)
2025-08-05 18:22:02.183 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (10/10)
2025-08-05 18:22:02.679 | WARNING  | backend.api_server:websocket_endpoint:794 - WebSocket连接请求的任务ID不存在: default
INFO:     connection closed
INFO:     ('127.0.0.1', 54921) - "WebSocket /ws/crawl/default" [accepted]
2025-08-05 18:22:06.030 | INFO     | backend.api_server:websocket_endpoint:782 - WebSocket连接已建立: task_id=default
2025-08-05 18:22:06.031 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (1/10)
INFO:     connection open
2025-08-05 18:22:06.556 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (2/10)
2025-08-05 18:22:07.052 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (3/10)
2025-08-05 18:22:07.549 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (4/10)
2025-08-05 18:22:08.030 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (5/10)
2025-08-05 18:22:08.558 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (6/10)
2025-08-05 18:22:09.070 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (7/10)
2025-08-05 18:22:09.581 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (8/10)
2025-08-05 18:22:10.077 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (9/10)
INFO:     127.0.0.1:54933 - "GET /api/status HTTP/1.1" 404 Not Found
2025-08-05 18:22:10.605 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (10/10)
2025-08-05 18:22:11.117 | WARNING  | backend.api_server:websocket_endpoint:794 - WebSocket连接请求的任务ID不存在: default
INFO:     connection closed
INFO:     ('127.0.0.1', 54955) - "WebSocket /ws/crawl/default" [accepted]
2025-08-05 18:22:15.428 | INFO     | backend.api_server:websocket_endpoint:782 - WebSocket连接已建立: task_id=default
2025-08-05 18:22:15.428 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (1/10)
INFO:     connection open
2025-08-05 18:22:15.939 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (2/10)
2025-08-05 18:22:16.465 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (3/10)
2025-08-05 18:22:16.975 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (4/10)
2025-08-05 18:22:17.487 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (5/10)
2025-08-05 18:22:17.983 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (6/10)
2025-08-05 18:22:18.512 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (7/10)
2025-08-05 18:22:19.024 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (8/10)
2025-08-05 18:22:19.519 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (9/10)
2025-08-05 18:22:20.031 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (10/10)
2025-08-05 18:22:20.559 | WARNING  | backend.api_server:websocket_endpoint:794 - WebSocket连接请求的任务ID不存在: default
INFO:     connection closed
INFO:     ('127.0.0.1', 54989) - "WebSocket /ws/crawl/default" [accepted]
2025-08-05 18:22:24.421 | INFO     | backend.api_server:websocket_endpoint:782 - WebSocket连接已建立: task_id=default
2025-08-05 18:22:24.422 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (1/10)
INFO:     connection open
2025-08-05 18:22:24.931 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (2/10)
2025-08-05 18:22:25.428 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (3/10)
2025-08-05 18:22:25.924 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (4/10)
2025-08-05 18:22:26.405 | INFO     | backend.api_server:websocket_endpoint:789 - 任务 default 尚未创建，等待中... (5/10)