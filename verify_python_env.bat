@echo off
setlocal EnableDelayedExpansion
chcp 65001 >nul

echo ================================================================================
echo                        Python 环境验证工具 v2.0
echo ================================================================================
echo.

REM 初始化变量
set PYTHON_FOUND=0
set PIP_FOUND=0
set PATH_OK=0
set TOTAL_TESTS=0
set PASSED_TESTS=0
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311

REM 创建临时Python脚本用于详细测试
echo import sys > temp_verify.py
echo import os >> temp_verify.py
echo import platform >> temp_verify.py
echo import site >> temp_verify.py
echo import subprocess >> temp_verify.py
echo. >> temp_verify.py
echo print("Python详细信息:") >> temp_verify.py
echo print(f"  版本: {sys.version}") >> temp_verify.py
echo print(f"  平台: {platform.platform()}") >> temp_verify.py
echo print(f"  架构: {platform.machine()}") >> temp_verify.py
echo print(f"  可执行文件: {sys.executable}") >> temp_verify.py
echo print(f"  前缀: {sys.prefix}") >> temp_verify.py
echo print() >> temp_verify.py
echo print("站点包目录:") >> temp_verify.py
echo for path in site.getsitepackages(): >> temp_verify.py
echo     print(f"  - {path}") >> temp_verify.py
echo print() >> temp_verify.py
echo print("用户站点包:") >> temp_verify.py
echo print(f"  - {site.getusersitepackages()}") >> temp_verify.py

echo ====================================
echo   基础环境检查
echo ====================================
echo.

REM 测试1: 检查Python命令
set /a TOTAL_TESTS+=1
echo [测试 1] 检查Python命令可用性...
where python >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   [✓] Python命令可用
    set PYTHON_FOUND=1
    set /a PASSED_TESTS+=1
    
    REM 获取第一个Python路径
    for /f "tokens=*" %%i in ('where python') do (
        set FIRST_PYTHON=%%i
        goto :PYTHON_PATH_CHECK
    )
    :PYTHON_PATH_CHECK
    echo   位置: !FIRST_PYTHON!
    
    REM 检查是否指向正确的Python
    echo !FIRST_PYTHON! | findstr /C:"Python311" >nul
    if %ERRORLEVEL% EQU 0 (
        echo   [✓] 指向正确的Python安装
    ) else (
        echo   [⚠] 警告：未指向预期的Python311安装
        echo !FIRST_PYTHON! | findstr /C:"WindowsApps" >nul
        if %ERRORLEVEL% EQU 0 (
            echo   [!] 检测到Windows Store Python，建议运行 disable_windows_store_python.bat
        )
    )
) else (
    echo   [✗] Python命令不可用
    set PYTHON_FOUND=0
)

echo.

REM 测试2: 检查pip命令
set /a TOTAL_TESTS+=1
echo [测试 2] 检查pip命令可用性...
where pip >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   [✓] pip命令可用
    set PIP_FOUND=1
    set /a PASSED_TESTS+=1
    
    for /f "tokens=*" %%i in ('where pip') do (
        set FIRST_PIP=%%i
        goto :PIP_PATH_CHECK
    )
    :PIP_PATH_CHECK
    echo   位置: !FIRST_PIP!
) else (
    echo   [✗] pip命令不可用
    set PIP_FOUND=0
)

echo.

REM 测试3: Python版本
set /a TOTAL_TESTS+=1
echo [测试 3] 检查Python版本...
if %PYTHON_FOUND% EQU 1 (
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo   版本: !PYTHON_VERSION!
    
    echo !PYTHON_VERSION! | findstr /C:"3.11" >nul
    if %ERRORLEVEL% EQU 0 (
        echo   [✓] Python 3.11系列
        set /a PASSED_TESTS+=1
    ) else (
        echo   [⚠] 非3.11版本，可能需要更新
    )
) else (
    echo   [✗] 无法获取Python版本
)

echo.

REM 测试4: pip版本
set /a TOTAL_TESTS+=1
echo [测试 4] 检查pip版本...
if %PIP_FOUND% EQU 1 (
    for /f "tokens=1,2" %%i in ('pip --version 2^>^&1') do (
        if "%%i"=="pip" set PIP_VERSION=%%j
    )
    echo   版本: pip !PIP_VERSION!
    echo   [✓] pip可用
    set /a PASSED_TESTS+=1
) else (
    echo   [✗] 无法获取pip版本
)

echo.
echo ====================================
echo   PATH环境变量分析
echo ====================================
echo.

REM 测试5: 检查PATH中的Python
set /a TOTAL_TESTS+=1
echo [测试 5] 分析PATH环境变量...

REM 获取PATH并分析
set PATH_COUNT=0
set PYTHON_PATH_COUNT=0
set DUPLICATE_COUNT=0

REM 使用PowerShell获取并分析PATH
powershell -Command "$env:PATH -split ';' | ForEach-Object { if ($_ -like '*Python*') { Write-Host $_ } }" > temp_paths.txt

REM 计算Python相关路径数量
for /f "tokens=*" %%i in (temp_paths.txt) do (
    set /a PYTHON_PATH_COUNT+=1
)

if %PYTHON_PATH_COUNT% GTR 0 (
    echo   找到 %PYTHON_PATH_COUNT% 个Python相关路径
    
    if %PYTHON_PATH_COUNT% GTR 2 (
        echo   [⚠] 警告：PATH中有多个Python路径，可能存在重复
        echo   建议运行 clean_python_path.ps1 清理重复项
    ) else (
        echo   [✓] PATH配置正常
        set /a PASSED_TESTS+=1
    )
    
    echo.
    echo   Python路径列表：
    type temp_paths.txt | findstr /N "^" | findstr "^[1-9]"
) else (
    echo   [✗] PATH中未找到Python路径
    echo   建议运行 fix_python_env_permanent.bat
)

del temp_paths.txt >nul 2>&1

echo.
echo ====================================
echo   环境变量检查
echo ====================================
echo.

REM 测试6: PYTHONHOME变量
set /a TOTAL_TESTS+=1
echo [测试 6] 检查PYTHONHOME环境变量...
if defined PYTHONHOME (
    echo   PYTHONHOME: %PYTHONHOME%
    if exist "%PYTHONHOME%\python.exe" (
        echo   [✓] PYTHONHOME指向有效的Python安装
        set /a PASSED_TESTS+=1
    ) else (
        echo   [⚠] PYTHONHOME指向的目录不包含python.exe
    )
) else (
    echo   [!] PYTHONHOME未设置（可选）
    set /a PASSED_TESTS+=1
)

echo.

REM 测试7: PYTHONPATH变量
set /a TOTAL_TESTS+=1
echo [测试 7] 检查PYTHONPATH环境变量...
if defined PYTHONPATH (
    echo   PYTHONPATH: %PYTHONPATH%
    echo   [!] 注意：设置PYTHONPATH可能影响包导入
) else (
    echo   [✓] PYTHONPATH未设置（正常）
)
set /a PASSED_TESTS+=1

echo.
echo ====================================
echo   功能测试
echo ====================================
echo.

REM 测试8: Python导入测试
set /a TOTAL_TESTS+=1
echo [测试 8] 测试Python标准库导入...
python -c "import sys, os, json, datetime, pathlib" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   [✓] 标准库导入成功
    set /a PASSED_TESTS+=1
) else (
    echo   [✗] 标准库导入失败
)

echo.

REM 测试9: pip功能测试
set /a TOTAL_TESTS+=1
echo [测试 9] 测试pip列表功能...
pip list --format=columns >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   [✓] pip list命令执行成功
    set /a PASSED_TESTS+=1
    
    REM 显示已安装包数量
    for /f %%i in ('pip list 2^>nul ^| find /c /v ""') do set PACKAGE_COUNT=%%i
    set /a PACKAGE_COUNT=!PACKAGE_COUNT!-2
    echo   已安装包数量: !PACKAGE_COUNT!
) else (
    echo   [✗] pip list命令失败
)

echo.

REM 测试10: 创建和运行简单脚本
set /a TOTAL_TESTS+=1
echo [测试 10] 测试Python脚本执行...
echo print("Hello from Python test!") > test_script.py
python test_script.py >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo   [✓] Python脚本执行成功
    set /a PASSED_TESTS+=1
) else (
    echo   [✗] Python脚本执行失败
)
del test_script.py >nul 2>&1

echo.
echo ====================================
echo   详细Python信息
echo ====================================
echo.

if %PYTHON_FOUND% EQU 1 (
    python temp_verify.py 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo [⚠] 无法获取详细信息
    )
) else (
    echo [✗] Python不可用，跳过详细信息
)

del temp_verify.py >nul 2>&1

echo.
echo ================================================================================
echo                              验证结果总结
echo ================================================================================
echo.

REM 计算成功率
set /a SUCCESS_RATE=PASSED_TESTS*100/TOTAL_TESTS

echo   测试总数: %TOTAL_TESTS%
echo   通过测试: %PASSED_TESTS%
echo   成功率: %SUCCESS_RATE%%%
echo.

if %SUCCESS_RATE% GEQ 90 (
    echo   [✓✓✓] 优秀！Python环境配置完美
    echo   状态: 🟢 正常运行
) else if %SUCCESS_RATE% GEQ 70 (
    echo   [✓✓] 良好！Python环境基本正常
    echo   状态: 🟡 需要小调整
    echo.
    echo   建议操作：
    if %PYTHON_PATH_COUNT% GTR 2 (
        echo   • 运行 clean_python_path.ps1 清理PATH重复项
    )
    if %PYTHON_FOUND% EQU 0 (
        echo   • 运行 fix_python_env_permanent.bat 修复环境
    )
) else if %SUCCESS_RATE% GEQ 50 (
    echo   [✓] 一般！Python环境需要修复
    echo   状态: 🟠 需要修复
    echo.
    echo   建议操作：
    echo   1. 运行 fix_python_env_permanent.bat（选择完整修复）
    echo   2. 重启命令行窗口
    echo   3. 再次运行此验证脚本
) else (
    echo   [✗] 严重！Python环境配置有问题
    echo   状态: 🔴 需要立即修复
    echo.
    echo   建议操作：
    echo   1. 以管理员身份运行 fix_python_env_permanent.bat
    echo   2. 选择"完整修复"选项
    echo   3. 重启计算机
    echo   4. 再次验证
)

echo.
echo ================================================================================
echo.

REM 生成详细报告
echo 是否生成详细报告文件？
set /p gen_report="输入 Y 生成报告，其他键跳过: "

if /i "%gen_report%"=="Y" (
    set REPORT_FILE=python_env_report_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
    set REPORT_FILE=!REPORT_FILE: =0!
    
    echo 生成报告: !REPORT_FILE!
    echo Python环境验证报告 > !REPORT_FILE!
    echo ==================== >> !REPORT_FILE!
    echo 生成时间: %date% %time% >> !REPORT_FILE!
    echo. >> !REPORT_FILE!
    echo 测试结果: %PASSED_TESTS%/%TOTAL_TESTS% (成功率: %SUCCESS_RATE%%%) >> !REPORT_FILE!
    echo. >> !REPORT_FILE!
    echo Python路径: >> !REPORT_FILE!
    where python >> !REPORT_FILE! 2>&1
    echo. >> !REPORT_FILE!
    echo Python版本: >> !REPORT_FILE!
    python --version >> !REPORT_FILE! 2>&1
    echo. >> !REPORT_FILE!
    echo pip版本: >> !REPORT_FILE!
    pip --version >> !REPORT_FILE! 2>&1
    echo. >> !REPORT_FILE!
    echo 已安装包: >> !REPORT_FILE!
    pip list >> !REPORT_FILE! 2>&1
    
    echo.
    echo [✓] 报告已生成: !REPORT_FILE!
)

echo.
pause
endlocal
exit /b 0