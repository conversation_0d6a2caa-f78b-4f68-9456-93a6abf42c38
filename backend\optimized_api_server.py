#!/usr/bin/env python3
"""
优化版拼多多爬虫API服务器
- 简化架构，提高稳定性
- 优化WebSocket连接管理
- 清晰的错误处理机制
- 模块化设计
"""

import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
import logging

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 切换到项目根目录
os.chdir(str(PROJECT_ROOT))

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据模型
class CrawlRequest(BaseModel):
    keywords: List[str]
    targetCount: int = 50
    headless: bool = True
    filterEnabled: bool = False

class CookieRequest(BaseModel):
    cookies: Optional[List[Dict[str, Any]]] = None
    cookieString: Optional[str] = None

# FastAPI应用实例
app = FastAPI(
    title="拼多多爬虫API (优化版)",
    description="简化且稳定的爬虫API服务",
    version="2.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173", "http://localhost:5174", 
        "http://localhost:5175", "http://localhost:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局状态管理
class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.websocket_connections: Dict[str, Set[WebSocket]] = {}
        
    def create_task(self, task_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建新任务"""
        task = {
            "id": task_id,
            "status": "created",
            "config": config,
            "progress": {"current": 0, "total": config.get("targetCount", 50), "percentage": 0},
            "data": [],
            "created_time": datetime.now().isoformat(),
            "start_time": None,
            "end_time": None,
            "error": None
        }
        self.tasks[task_id] = task
        return task
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def update_task_status(self, task_id: str, status: str, **kwargs):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = status
            self.tasks[task_id].update(kwargs)
    
    def add_websocket(self, task_id: str, websocket: WebSocket):
        """添加WebSocket连接"""
        if task_id not in self.websocket_connections:
            self.websocket_connections[task_id] = set()
        self.websocket_connections[task_id].add(websocket)
    
    def remove_websocket(self, task_id: str, websocket: WebSocket):
        """移除WebSocket连接"""
        if task_id in self.websocket_connections:
            self.websocket_connections[task_id].discard(websocket)
            if not self.websocket_connections[task_id]:
                del self.websocket_connections[task_id]
    
    async def broadcast_to_task(self, task_id: str, message: Dict[str, Any]):
        """向任务的所有WebSocket连接广播消息"""
        if task_id not in self.websocket_connections:
            return
        
        disconnected = []
        for websocket in self.websocket_connections[task_id].copy():
            try:
                await websocket.send_json(message)
            except (WebSocketDisconnect, RuntimeError):
                disconnected.append(websocket)
        
        # 清理断开的连接
        for websocket in disconnected:
            self.remove_websocket(task_id, websocket)

# 全局任务管理器
task_manager = TaskManager()

# Cookie管理
class SimpleCookieManager:
    def __init__(self):
        self.cookie_file = PROJECT_ROOT / "browser_data" / "cookies" / "pdd_cookies.json"
        self.cookie_file.parent.mkdir(parents=True, exist_ok=True)
    
    def load_cookies(self) -> List[Dict[str, Any]]:
        """加载Cookie"""
        try:
            if self.cookie_file.exists():
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('cookies', [])
        except Exception as e:
            logger.error(f"加载Cookie失败: {e}")
        return []
    
    def save_cookies(self, cookies: List[Dict[str, Any]]) -> bool:
        """保存Cookie"""
        try:
            data = {
                "cookies": cookies,
                "timestamp": datetime.now().isoformat(),
                "count": len(cookies)
            }
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存Cookie失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取Cookie状态"""
        cookies = self.load_cookies()
        return {
            "isValid": len(cookies) > 0,
            "count": len(cookies),
            "lastUpdate": datetime.now().isoformat() if cookies else None
        }

cookie_manager = SimpleCookieManager()

# 模拟爬虫类（用于测试）
class MockCrawler:
    def __init__(self, task_id: str, config: Dict[str, Any]):
        self.task_id = task_id
        self.config = config
        self.is_running = False
        self.collected_data = []
        
    async def start_crawling(self):
        """模拟爬虫运行"""
        self.is_running = True
        task_manager.update_task_status(self.task_id, "running", start_time=datetime.now().isoformat())
        
        try:
            keywords = self.config.get("keywords", [])
            target_count = self.config.get("targetCount", 50)
            
            # 模拟爬取过程
            for i, keyword in enumerate(keywords):
                if not self.is_running:
                    break
                    
                # 广播关键词开始
                await task_manager.broadcast_to_task(self.task_id, {
                    "type": "keyword_started",
                    "data": {"keyword": keyword, "index": i}
                })
                
                # 模拟收集数据
                for j in range(target_count // len(keywords)):
                    if not self.is_running:
                        break
                        
                    # 模拟商品数据
                    product = {
                        "goods_id": f"mock_{keyword}_{j}",
                        "goods_name": f"模拟商品 {keyword} {j+1}",
                        "price": f"{(j+1)*10}.00",
                        "sales": f"{(j+1)*100}人已拼",
                        "keyword": keyword,
                        "created_time": datetime.now().isoformat()
                    }
                    
                    self.collected_data.append(product)
                    
                    # 更新进度
                    current = len(self.collected_data)
                    total = target_count
                    percentage = (current / total * 100) if total > 0 else 0
                    
                    progress = {
                        "current": current,
                        "total": total,
                        "percentage": percentage,
                        "currentKeyword": keyword
                    }
                    
                    # 广播进度和数据
                    await task_manager.broadcast_to_task(self.task_id, {
                        "type": "progress",
                        "data": progress
                    })
                    
                    await task_manager.broadcast_to_task(self.task_id, {
                        "type": "data",
                        "data": [product]
                    })
                    
                    # 模拟延迟
                    await asyncio.sleep(0.5)
                
                # 广播关键词完成
                await task_manager.broadcast_to_task(self.task_id, {
                    "type": "keyword_completed",
                    "data": {"keyword": keyword, "index": i}
                })
            
            # 完成
            task_manager.update_task_status(
                self.task_id, 
                "completed", 
                end_time=datetime.now().isoformat(),
                data=self.collected_data
            )
            
            await task_manager.broadcast_to_task(self.task_id, {
                "type": "completed",
                "data": {
                    "success": True,
                    "totalGoods": len(self.collected_data),
                    "message": "爬取完成"
                }
            })
            
        except Exception as e:
            logger.error(f"爬虫运行失败: {e}")
            task_manager.update_task_status(self.task_id, "failed", error=str(e))
            await task_manager.broadcast_to_task(self.task_id, {
                "type": "error",
                "data": {"message": str(e)}
            })
        finally:
            self.is_running = False

# API路由
@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "拼多多爬虫API服务运行中 (优化版)",
        "version": "2.0.0",
        "status": "healthy"
    }

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0"
    }

@app.post("/api/crawl/start")
async def start_crawl(request: CrawlRequest, background_tasks: BackgroundTasks):
    """启动爬虫任务"""
    task_id = str(uuid.uuid4())

    try:
        # 创建任务
        config = {
            "keywords": request.keywords,
            "targetCount": request.targetCount,
            "headless": request.headless,
            "filterEnabled": request.filterEnabled
        }

        task = task_manager.create_task(task_id, config)

        # 创建爬虫实例
        crawler = MockCrawler(task_id, config)

        # 在后台运行爬虫
        background_tasks.add_task(crawler.start_crawling)

        logger.info(f"启动爬虫任务: {task_id}")

        return {
            "success": True,
            "taskId": task_id,
            "message": "爬虫任务已启动",
            "config": config
        }

    except Exception as e:
        logger.error(f"启动爬虫失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/crawl/{task_id}/status")
async def get_crawl_status(task_id: str):
    """获取爬虫任务状态"""
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    return {
        "taskId": task_id,
        "status": task["status"],
        "progress": task["progress"],
        "startTime": task.get("start_time"),
        "endTime": task.get("end_time"),
        "error": task.get("error"),
        "config": task["config"]
    }

@app.get("/api/crawl/{task_id}/preview")
async def get_preview_data(task_id: str, limit: int = 20):
    """获取预览数据"""
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    data = task.get("data", [])
    preview_data = data[-limit:] if len(data) > limit else data

    return {
        "taskId": task_id,
        "data": preview_data,
        "total": len(data)
    }

@app.post("/api/crawl/{task_id}/stop")
async def stop_crawl(task_id: str):
    """停止爬虫任务"""
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 这里应该停止实际的爬虫，现在只是更新状态
    task_manager.update_task_status(task_id, "stopped", end_time=datetime.now().isoformat())

    return {"success": True, "message": "任务已停止"}

# Cookie相关API
@app.get("/api/cookie/status")
async def get_cookie_status():
    """获取Cookie状态"""
    try:
        status = cookie_manager.get_status()
        return status
    except Exception as e:
        logger.error(f"获取Cookie状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cookie/save")
async def save_cookie(request: CookieRequest):
    """保存Cookie"""
    try:
        cookies = request.cookies or []

        # 如果提供了cookie字符串，解析它
        if request.cookieString:
            parsed_cookies = []
            for item in request.cookieString.split(';'):
                item = item.strip()
                if '=' in item:
                    name, value = item.split('=', 1)
                    parsed_cookies.append({
                        "name": name.strip(),
                        "value": value.strip(),
                        "domain": ".yangkeduo.com"
                    })
            cookies.extend(parsed_cookies)

        success = cookie_manager.save_cookies(cookies)

        if success:
            return {
                "success": True,
                "message": f"已保存 {len(cookies)} 个Cookie",
                "count": len(cookies)
            }
        else:
            raise HTTPException(status_code=500, detail="保存Cookie失败")

    except Exception as e:
        logger.error(f"保存Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/cookie/clear")
async def clear_cookie():
    """清除Cookie"""
    try:
        success = cookie_manager.save_cookies([])
        if success:
            return {"success": True, "message": "Cookie已清除"}
        else:
            raise HTTPException(status_code=500, detail="清除Cookie失败")
    except Exception as e:
        logger.error(f"清除Cookie失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket路由
@app.websocket("/ws/crawl/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket连接处理"""
    await websocket.accept()
    logger.info(f"WebSocket连接已建立: task_id={task_id}")

    # 添加到连接池
    task_manager.add_websocket(task_id, websocket)

    try:
        # 发送连接确认
        await websocket.send_json({
            "type": "connected",
            "data": {"taskId": task_id, "timestamp": datetime.now().isoformat()}
        })

        # 如果任务存在，发送当前状态
        task = task_manager.get_task(task_id)
        if task:
            await websocket.send_json({
                "type": "status",
                "data": {
                    "status": task["status"],
                    "progress": task["progress"]
                }
            })

        # 保持连接活跃
        while True:
            try:
                # 等待客户端消息（心跳等）
                message = await asyncio.wait_for(websocket.receive_json(), timeout=30.0)

                # 处理心跳
                if message.get("type") == "ping":
                    await websocket.send_json({"type": "pong"})

            except asyncio.TimeoutError:
                # 发送心跳
                await websocket.send_json({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                })
            except WebSocketDisconnect:
                break

    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: task_id={task_id}")
    except Exception as e:
        logger.error(f"WebSocket处理错误: {e}")
    finally:
        # 清理连接
        task_manager.remove_websocket(task_id, websocket)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='优化版拼多多爬虫API服务器')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务器地址')
    parser.add_argument('--reload', action='store_true', help='开启自动重载')
    args = parser.parse_args()

    logger.info(f"启动优化版API服务器...")
    logger.info(f"服务地址: http://{args.host}:{args.port}")
    logger.info(f"API文档: http://{args.host}:{args.port}/docs")

    uvicorn.run(
        "optimized_api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )
