const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 测试执行和报告生成脚本
 * 统一执行所有类型的测试并生成综合报告
 */

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      e2e: null,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0
      }
    };
    
    this.startTime = Date.now();
    this.testResultsDir = path.join(__dirname, '../test-results');
    this.mockServerProcess = null;
  }

  // 创建测试结果目录
  ensureResultsDir() {
    if (!fs.existsSync(this.testResultsDir)) {
      fs.mkdirSync(this.testResultsDir, { recursive: true });
    }
  }

  // 启动Mock服务器
  async startMockServer() {
    console.log('🚀 启动Mock服务器...');
    
    return new Promise((resolve, reject) => {
      const mockServerPath = path.join(__dirname, 'helpers/mock-server.js');
      this.mockServerProcess = spawn('node', [mockServerPath], {
        stdio: 'pipe'
      });

      this.mockServerProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Running on http://localhost:8001')) {
          console.log('✅ Mock服务器启动成功');
          resolve();
        }
      });

      this.mockServerProcess.stderr.on('data', (data) => {
        console.error('Mock服务器错误:', data.toString());
      });

      this.mockServerProcess.on('error', (error) => {
        console.error('启动Mock服务器失败:', error);
        reject(error);
      });

      // 设置启动超时
      setTimeout(() => {
        reject(new Error('Mock服务器启动超时'));
      }, 10000);
    });
  }

  // 停止Mock服务器
  stopMockServer() {
    if (this.mockServerProcess) {
      console.log('🛑 停止Mock服务器...');
      this.mockServerProcess.kill('SIGTERM');
      this.mockServerProcess = null;
    }
  }

  // 运行命令并捕获结果
  async runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      console.log(`\n📋 执行命令: ${command} ${args.join(' ')}`);
      
      const process = spawn(command, args, {
        stdio: 'pipe',
        shell: true,
        ...options
      });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        console.log(output);
      });

      process.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        console.error(output);
      });

      process.on('close', (code) => {
        resolve({
          code,
          stdout,
          stderr,
          success: code === 0
        });
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  // 解析Jest测试结果
  parseJestResults(output) {
    const results = {
      tests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    };

    try {
      // 解析Jest输出
      const testMatch = output.match(/Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/);
      if (testMatch) {
        results.failed = parseInt(testMatch[1]);
        results.passed = parseInt(testMatch[2]);
        results.tests = parseInt(testMatch[3]);
      } else {
        const passedMatch = output.match(/Tests:\s+(\d+)\s+passed,\s+(\d+)\s+total/);
        if (passedMatch) {
          results.passed = parseInt(passedMatch[1]);
          results.tests = parseInt(passedMatch[2]);
        }
      }

      const durationMatch = output.match(/Time:\s+([\d.]+)\s*s/);
      if (durationMatch) {
        results.duration = parseFloat(durationMatch[1]) * 1000;
      }

      results.skipped = results.tests - results.passed - results.failed;
    } catch (error) {
      console.warn('解析Jest结果失败:', error);
    }

    return results;
  }

  // 解析Playwright测试结果
  parsePlaywrightResults(output) {
    const results = {
      tests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    };

    try {
      // 解析Playwright输出
      const summaryMatch = output.match(/(\d+)\s+passed.*?(\d+)\s+failed.*?(\d+)\s+skipped/);
      if (summaryMatch) {
        results.passed = parseInt(summaryMatch[1]);
        results.failed = parseInt(summaryMatch[2]);
        results.skipped = parseInt(summaryMatch[3]);
        results.tests = results.passed + results.failed + results.skipped;
      }

      const durationMatch = output.match(/(\d+)ms/);
      if (durationMatch) {
        results.duration = parseInt(durationMatch[1]);
      }
    } catch (error) {
      console.warn('解析Playwright结果失败:', error);
    }

    return results;
  }

  // 运行单元测试
  async runUnitTests() {
    console.log('\n🧪 运行单元测试...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:unit', '--', '--coverage', '--passWithNoTests']);
      const parsed = this.parseJestResults(result.stdout);
      
      this.results.unit = {
        ...parsed,
        success: result.success,
        output: result.stdout
      };

      console.log(`✅ 单元测试完成: ${parsed.passed}通过, ${parsed.failed}失败, ${parsed.skipped}跳过`);
      return result.success;
    } catch (error) {
      console.error('❌ 单元测试执行失败:', error);
      this.results.unit = {
        tests: 0,
        passed: 0,
        failed: 1,
        skipped: 0,
        duration: 0,
        success: false,
        error: error.message
      };
      return false;
    }
  }

  // 运行集成测试
  async runIntegrationTests() {
    console.log('\n🔗 运行集成测试...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:integration', '--', '--passWithNoTests']);
      const parsed = this.parseJestResults(result.stdout);
      
      this.results.integration = {
        ...parsed,
        success: result.success,
        output: result.stdout
      };

      console.log(`✅ 集成测试完成: ${parsed.passed}通过, ${parsed.failed}失败, ${parsed.skipped}跳过`);
      return result.success;
    } catch (error) {
      console.error('❌ 集成测试执行失败:', error);
      this.results.integration = {
        tests: 0,
        passed: 0,
        failed: 1,
        skipped: 0,
        duration: 0,
        success: false,
        error: error.message
      };
      return false;
    }
  }

  // 运行端到端测试
  async runE2ETests() {
    console.log('\n🌐 运行端到端测试...');
    
    try {
      const result = await this.runCommand('npx', ['playwright', 'test', '--reporter=line']);
      const parsed = this.parsePlaywrightResults(result.stdout);
      
      this.results.e2e = {
        ...parsed,
        success: result.success,
        output: result.stdout
      };

      console.log(`✅ E2E测试完成: ${parsed.passed}通过, ${parsed.failed}失败, ${parsed.skipped}跳过`);
      return result.success;
    } catch (error) {
      console.error('❌ E2E测试执行失败:', error);
      this.results.e2e = {
        tests: 0,
        passed: 0,
        failed: 1,
        skipped: 0,
        duration: 0,
        success: false,
        error: error.message
      };
      return false;
    }
  }

  // 计算汇总统计
  calculateSummary() {
    const testTypes = ['unit', 'integration', 'e2e'];
    
    this.results.summary = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      coverage: null
    };

    testTypes.forEach(type => {
      const result = this.results[type];
      if (result) {
        this.results.summary.total += result.tests || 0;
        this.results.summary.passed += result.passed || 0;
        this.results.summary.failed += result.failed || 0;
        this.results.summary.skipped += result.skipped || 0;
        this.results.summary.duration += result.duration || 0;
      }
    });

    this.results.summary.totalDuration = Date.now() - this.startTime;
    this.results.summary.successRate = this.results.summary.total > 0 
      ? (this.results.summary.passed / this.results.summary.total * 100).toFixed(2)
      : 0;
  }

  // 生成HTML报告
  generateHTMLReport() {
    const reportPath = path.join(this.testResultsDir, 'test-report.html');
    
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼多多爬虫前端测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .title { color: #1890ff; margin-bottom: 10px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f9f9f9; padding: 20px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #666; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #1890ff; }
        .success-rate { color: #52c41a; }
        .test-section { margin-bottom: 30px; }
        .test-section h2 { color: #333; border-bottom: 2px solid #1890ff; padding-bottom: 10px; }
        .test-result { background: #f9f9f9; padding: 15px; border-radius: 6px; margin-bottom: 15px; }
        .status { padding: 4px 12px; border-radius: 4px; color: white; font-weight: bold; }
        .status.success { background: #52c41a; }
        .status.failure { background: #ff4d4f; }
        .status.warning { background: #faad14; }
        .metrics { display: flex; gap: 20px; margin-top: 10px; }
        .metric { text-align: center; }
        .metric-value { font-size: 1.2em; font-weight: bold; color: #1890ff; }
        .metric-label { font-size: 0.9em; color: #666; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #a8071a; padding: 10px; border-radius: 4px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">拼多多爬虫前端自动化测试报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number">${this.results.summary.total}</div>
            </div>
            <div class="summary-card">
                <h3>通过测试</h3>
                <div class="number" style="color: #52c41a;">${this.results.summary.passed}</div>
            </div>
            <div class="summary-card">
                <h3>失败测试</h3>
                <div class="number" style="color: #ff4d4f;">${this.results.summary.failed}</div>
            </div>
            <div class="summary-card">
                <h3>跳过测试</h3>
                <div class="number" style="color: #faad14;">${this.results.summary.skipped}</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="number success-rate">${this.results.summary.successRate}%</div>
            </div>
            <div class="summary-card">
                <h3>总耗时</h3>
                <div class="number">${Math.round(this.results.summary.totalDuration / 1000)}s</div>
            </div>
        </div>

        ${this.generateTestSectionHTML('单元测试', 'unit')}
        ${this.generateTestSectionHTML('集成测试', 'integration')}
        ${this.generateTestSectionHTML('端到端测试', 'e2e')}

        <div class="footer">
            <p>🤖 由 Claude Code 自动化测试系统生成</p>
            <p>测试框架: Jest + Playwright | Mock服务器: Express + WebSocket</p>
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html, 'utf8');
    console.log(`📄 HTML报告已生成: ${reportPath}`);
    return reportPath;
  }

  // 生成测试章节HTML
  generateTestSectionHTML(title, type) {
    const result = this.results[type];
    if (!result) return '';

    const statusClass = result.success ? 'success' : 'failure';
    const statusText = result.success ? '通过' : '失败';

    return `
        <div class="test-section">
            <h2>${title}</h2>
            <div class="test-result">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>${title}</h3>
                    <span class="status ${statusClass}">${statusText}</span>
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-value">${result.tests || 0}</div>
                        <div class="metric-label">总数</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #52c41a;">${result.passed || 0}</div>
                        <div class="metric-label">通过</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #ff4d4f;">${result.failed || 0}</div>
                        <div class="metric-label">失败</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #faad14;">${result.skipped || 0}</div>
                        <div class="metric-label">跳过</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${Math.round((result.duration || 0) / 1000)}s</div>
                        <div class="metric-label">耗时</div>
                    </div>
                </div>
                ${result.error ? `<div class="error">错误: ${result.error}</div>` : ''}
            </div>
        </div>`;
  }

  // 生成JSON报告
  generateJSONReport() {
    const reportPath = path.join(this.testResultsDir, 'test-results.json');
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.results.summary,
      results: {
        unit: this.results.unit,
        integration: this.results.integration,
        e2e: this.results.e2e
      },
      environment: {
        node: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    console.log(`📊 JSON报告已生成: ${reportPath}`);
    return reportPath;
  }

  // 输出控制台摘要
  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 测试执行摘要');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.results.summary.total}`);
    console.log(`✅ 通过: ${this.results.summary.passed}`);
    console.log(`❌ 失败: ${this.results.summary.failed}`);
    console.log(`⏭️ 跳过: ${this.results.summary.skipped}`);
    console.log(`📈 成功率: ${this.results.summary.successRate}%`);
    console.log(`⏱️ 总耗时: ${Math.round(this.results.summary.totalDuration / 1000)}秒`);
    console.log('='.repeat(60));

    if (this.results.summary.failed > 0) {
      console.log('❌ 有测试失败，请查看详细报告');
      return false;
    } else {
      console.log('🎉 所有测试通过！');
      return true;
    }
  }

  // 主执行方法
  async run() {
    console.log('🚀 开始执行拼多多爬虫前端自动化测试...');
    
    try {
      // 准备测试环境
      this.ensureResultsDir();
      
      // 启动Mock服务器
      await this.startMockServer();
      
      // 等待服务器稳定
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 并行运行测试（或串行，根据需要调整）
      const testPromises = [
        this.runUnitTests(),
        // this.runIntegrationTests(), // 可以并行或串行
        // this.runE2ETests()
      ];

      // 串行运行集成和E2E测试（避免资源冲突）
      await Promise.all(testPromises);
      await this.runIntegrationTests();
      await this.runE2ETests();

      // 计算汇总
      this.calculateSummary();

      // 生成报告
      this.generateHTMLReport();
      this.generateJSONReport();

      // 输出摘要
      const allPassed = this.printSummary();

      // 停止Mock服务器
      this.stopMockServer();

      // 退出码
      process.exit(allPassed ? 0 : 1);

    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      this.stopMockServer();
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const runner = new TestRunner();
  
  // 处理退出信号
  process.on('SIGINT', () => {
    console.log('\n⚠️ 收到退出信号，正在清理...');
    runner.stopMockServer();
    process.exit(1);
  });

  process.on('SIGTERM', () => {
    console.log('\n⚠️ 收到终止信号，正在清理...');
    runner.stopMockServer();
    process.exit(1);
  });

  runner.run().catch(error => {
    console.error('❌ 执行失败:', error);
    runner.stopMockServer();
    process.exit(1);
  });
}

module.exports = TestRunner;